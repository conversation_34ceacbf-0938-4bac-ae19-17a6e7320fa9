"use client";

import * as React from "react";
import {
  useKeyboardShortcuts,
  COMMON_SHORTCUTS,
} from "@/lib/keyboard-shortcuts";
import {
  HelpCircle,
  X,
  Search,
  Home,
  LayoutDashboard,
  Settings,
  User,
  FileText,
  Bell,
  Zap,
  Sun,
  Moon,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  Save,
  Printer,
  Download,
  Upload,
  Keyboard,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Tipos para los atajos de teclado
interface KeyboardShortcut {
  id: string;
  keys: string[];
  description: string;
  icon?: React.ElementType;
  category: "navigation" | "actions" | "tools" | "system";
}

// Lista de atajos de teclado
const keyboardShortcuts: KeyboardShortcut[] = [
  // Navegación
  {
    id: "command-palette",
    keys: ["Ctrl", "K"],
    description: "Abrir paleta de comandos",
    icon: Search,
    category: "navigation",
  },
  {
    id: "dashboard",
    keys: ["Alt", "D"],
    description: "Ir al Dashboard",
    icon: LayoutDashboard,
    category: "navigation",
  },
  {
    id: "settings",
    keys: ["Alt", "S"],
    description: "Ir a configuración",
    icon: Settings,
    category: "navigation",
  },
  {
    id: "back",
    keys: ["Alt", "←"],
    description: "Volver atrás",
    icon: ArrowLeft,
    category: "navigation",
  },
  {
    id: "forward",
    keys: ["Alt", "→"],
    description: "Ir adelante",
    icon: ArrowRight,
    category: "navigation",
  },

  // Acciones
  {
    id: "quick-actions",
    keys: ["Alt", "Q"],
    description: "Abrir acciones rápidas",
    icon: Zap,
    category: "actions",
  },
  {
    id: "notifications",
    keys: ["Alt", "N"],
    description: "Abrir notificaciones",
    icon: Bell,
    category: "actions",
  },
  {
    id: "new-report",
    keys: ["Alt", "R"],
    description: "Crear nuevo informe",
    icon: FileText,
    category: "actions",
  },
  {
    id: "save",
    keys: ["Ctrl", "S"],
    description: "Guardar cambios",
    icon: Save,
    category: "actions",
  },
  {
    id: "print",
    keys: ["Ctrl", "P"],
    description: "Imprimir",
    icon: Printer,
    category: "actions",
  },
  {
    id: "export",
    keys: ["Ctrl", "E"],
    description: "Exportar datos",
    icon: Download,
    category: "actions",
  },
  {
    id: "import",
    keys: ["Ctrl", "I"],
    description: "Importar datos",
    icon: Upload,
    category: "actions",
  },

  // Herramientas
  {
    id: "calculator",
    keys: ["Alt", "C"],
    description: "Abrir calculadora hidráulica",
    icon: Search,
    category: "tools",
  },

  // Sistema
  {
    id: "theme-toggle",
    keys: ["Alt", "T"],
    description: "Cambiar tema (claro/oscuro)",
    icon: Sun,
    category: "system",
  },
  {
    id: "refresh",
    keys: ["F5"],
    description: "Recargar página",
    icon: RefreshCw,
    category: "system",
  },
  {
    id: "help",
    keys: ["?"],
    description: "Mostrar atajos de teclado",
    icon: HelpCircle,
    category: "system",
  },
];

export function KeyboardShortcutsHelp() {
  const [isOpen, setIsOpen] = React.useState(false);

  // Implementar acciones para cada atajo
  useKeyboardShortcuts([
    // Navegación - NO implementar Ctrl+K aquí porque ya lo maneja CommandPalette
    {
      keys: ["Alt", "D"],
      description: "Ir al Dashboard",
      action: () => window.location.href = "/dashboard",
    },
    {
      keys: ["Alt", "S"],
      description: "Ir a configuración",
      action: () => window.location.href = "/dashboard/ajustes",
    },
    {
      keys: ["Alt", "←"],
      description: "Volver atrás",
      action: () => window.history.back(),
    },
    {
      keys: ["Alt", "→"],
      description: "Ir adelante",
      action: () => window.history.forward(),
    },
    // Acciones
    {
      keys: ["Alt", "Q"],
      description: "Abrir acciones rápidas",
      action: () => {},
    },
    {
      keys: ["Alt", "N"],
      description: "Abrir notificaciones",
      action: () => {},
    },
    {
      keys: ["Alt", "R"],
      description: "Crear nuevo informe",
      action: () => {},
    },
    {
      keys: ["Ctrl", "S"],
      description: "Guardar cambios",
      action: () => {},
    },
    {
      keys: ["Ctrl", "P"],
      description: "Imprimir",
      action: () => window.print(),
    },
    {
      keys: ["Ctrl", "E"],
      description: "Exportar datos",
      action: () => {},
    },
    {
      keys: ["Ctrl", "I"],
      description: "Importar datos",
      action: () => {},
    },
    // Herramientas
    {
      keys: ["Alt", "C"],
      description: "Abrir calculadora hidráulica",
      action: () => window.location.href = "/dashboard/tools/calculator",
    },
    // Sistema
    {
      keys: ["Alt", "T"],
      description: "Cambiar tema (claro/oscuro)",
      action: () => document.body.classList.toggle("dark"),
    },
    {
      keys: ["F5"],
      description: "Recargar página",
      action: () => window.location.reload(),
    },
    {
      keys: ["?"],
      description: "Mostrar atajos de teclado",
      action: () => setIsOpen((prev: any) => !prev),
    },
    {
      key: "Escape",
      description: "Cerrar panel de atajos",
      action: () => isOpen && setIsOpen(false),
      preventDefault: false,
    },
  ]);

  // Filtrar atajos por categoría
  const getShortcutsByCategory = (category: string) => {
    return keyboardShortcuts.filter(
      (shortcut) => shortcut.category === category
    );
  };

  // Renderizar una tecla
  const renderKey = (key: string) => (
    <span className="inline-flex items-center justify-center min-w-[28px] h-7 px-1.5 bg-blue-900/20 border border-blue-900/30 rounded text-xs font-medium text-blue-400">
      {key}
    </span>
  );

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-10 w-10 rounded-md text-blue-400 hover:bg-blue-900/20 hover:text-blue-300 transition-colors"
        onClick={() => setIsOpen(true)}
        aria-label="Atajos de teclado"
      >
        <Keyboard className="h-5 w-5" />
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-[1004] flex items-center justify-center bg-white/80 dark:bg-black/80 backdrop-blur-sm"
          >
            <div className="w-full max-w-2xl bg-white dark:bg-black/90 border border-gray-200 dark:border-blue-900/30 rounded-xl shadow-xl p-6 relative">
              <div className="flex items-center justify-between p-4 border-b border-blue-900/20">
                <h2 className="text-xl font-medium text-blue-400 flex items-center gap-2">
                  <Keyboard className="h-5 w-5" />
                  Atajos de Teclado
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                  className="h-8 w-8 rounded-full hover:bg-blue-900/20"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <Tabs defaultValue="navigation" className="w-full">
                <div className="border-b border-blue-900/20">
                  <TabsList className="w-full h-auto p-0 bg-transparent border-b border-blue-900/10">
                    <TabsTrigger
                      value="navigation"
                      className="flex-1 rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:shadow-none py-2 text-sm"
                    >
                      Navegación
                    </TabsTrigger>
                    <TabsTrigger
                      value="actions"
                      className="flex-1 rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:shadow-none py-2 text-sm"
                    >
                      Acciones
                    </TabsTrigger>
                    <TabsTrigger
                      value="tools"
                      className="flex-1 rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:shadow-none py-2 text-sm"
                    >
                      Herramientas
                    </TabsTrigger>
                    <TabsTrigger
                      value="system"
                      className="flex-1 rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:shadow-none py-2 text-sm"
                    >
                      Sistema
                    </TabsTrigger>
                  </TabsList>
                </div>

                {["navigation", "actions", "tools", "system"].map(
                  (category) => (
                    <TabsContent
                      key={category}
                      value={category}
                      className="m-0"
                    >
                      <ScrollArea className="h-[400px] p-4">
                        <div className="space-y-3">
                          {getShortcutsByCategory(category).map((shortcut) => (
                            <div
                              key={shortcut.id}
                              className="flex items-center justify-between p-3 rounded-lg border border-blue-900/20 bg-blue-900/5 hover:bg-blue-900/10 transition-colors"
                            >
                              <div className="flex items-center gap-3">
                                {shortcut.icon && (
                                  <div className="flex h-8 w-8 items-center justify-center rounded-md bg-blue-900/20 text-blue-400">
                                    <shortcut.icon className="h-4 w-4" />
                                  </div>
                                )}
                                <span className="text-sm">
                                  {shortcut.description}
                                </span>
                              </div>
                              <div className="flex items-center gap-1">
                                {shortcut.keys.map((key, index) => (
                                  <React.Fragment key={index}>
                                    {index > 0 && (
                                      <span className="mx-1 text-muted-foreground">
                                        +
                                      </span>
                                    )}
                                    {renderKey(key)}
                                  </React.Fragment>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </TabsContent>
                  )
                )}
              </Tabs>

              <div className="p-4 border-t border-blue-900/20 text-center text-sm text-muted-foreground">
                Presiona <span className="font-medium text-blue-400">?</span> en
                cualquier momento para mostrar esta ayuda
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
