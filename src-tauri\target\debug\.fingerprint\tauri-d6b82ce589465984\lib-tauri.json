{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"devtools\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2904254974226812342, "deps": [[40386456601120721, "percent_encoding", false, 16923689341187554613], [442785307232013896, "tauri_runtime", false, 11806504123871845879], [1200537532907108615, "url<PERSON><PERSON>n", false, 9895719282306968870], [3150220818285335163, "url", false, 10302015290578283650], [4143744114649553716, "raw_window_handle", false, 6880312038169398310], [4341921533227644514, "muda", false, 9704114858057968312], [4919829919303820331, "serialize_to_javascript", false, 15346930483855183548], [5986029879202738730, "log", false, 5130091804064732190], [7303982924001358866, "tokio", false, 1342577411973725596], [7752760652095876438, "tauri_runtime_wry", false, 4031737791577678827], [8539587424388551196, "webview2_com", false, 18339571700300879840], [9010263965687315507, "http", false, 11912463509730526472], [9228235415475680086, "tauri_macros", false, 2749158092779506669], [9689903380558560274, "serde", false, 9029599480544516048], [9920160576179037441, "getrandom", false, 5612414661966784049], [10229185211513642314, "mime", false, 3243225773130811609], [10629569228670356391, "futures_util", false, 8791724364475240372], [10755362358622467486, "build_script_build", false, 933888942641122212], [10806645703491011684, "thiserror", false, 15557766392623119489], [11050281405049894993, "tauri_utils", false, 15791428986117739117], [11989259058781683633, "dunce", false, 6511959509659229963], [12565293087094287914, "window_vibrancy", false, 6028785285214781418], [12986574360607194341, "serde_repr", false, 6591643943471775509], [13077543566650298139, "heck", false, 6236491847068976086], [13116089016666501665, "windows", false, 13473427962125974306], [13625485746686963219, "anyhow", false, 11700001983982356752], [15367738274754116744, "serde_json", false, 1424998972824055668], [16928111194414003569, "dirs", false, 17785411640586153240], [17155886227862585100, "glob", false, 15406231137402787690]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-d6b82ce589465984\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}