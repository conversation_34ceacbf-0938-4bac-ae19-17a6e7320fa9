{"rustc": 10895048813736897673, "features": "[\"devtools\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 14648588457211574579, "deps": [[376837177317575824, "softbuffer", false, 7430897782229348718], [442785307232013896, "tauri_runtime", false, 11806504123871845879], [3150220818285335163, "url", false, 10302015290578283650], [3722963349756955755, "once_cell", false, 17797913802642721045], [4143744114649553716, "raw_window_handle", false, 6880312038169398310], [5986029879202738730, "log", false, 5130091804064732190], [7752760652095876438, "build_script_build", false, 1499247029687616703], [8539587424388551196, "webview2_com", false, 18339571700300879840], [9010263965687315507, "http", false, 11912463509730526472], [11050281405049894993, "tauri_utils", false, 15791428986117739117], [13116089016666501665, "windows", false, 13473427962125974306], [13223659721939363523, "tao", false, 3522530663515030778], [14794439852947137341, "wry", false, 622737088691027247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-5b41a57f165df508\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}