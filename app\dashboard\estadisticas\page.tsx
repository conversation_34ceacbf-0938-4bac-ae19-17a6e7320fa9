"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTauri } from "@/contexts/TauriContext";
import {
  BarChart3,
  Activity,
  Cpu,
  MemoryStick,
  Clock,
  LineChart,
  PieChart,
  LayoutDashboard,
  RefreshCw,
  Calendar,
  Layers,
  Zap
} from "lucide-react";
import { SystemResourcesPanel } from "./components/SystemResourcesPanel";
import { ApplicationUsagePanel } from "./components/ApplicationUsagePanel";
import { UserBehaviorPanel } from "./components/UserBehaviorPanel";

export default function EstadisticasPage() {
  const { isTauri } = useTauri();
  const [activeTab, setActiveTab] = useState("system-resources");
  const [loading, setLoading] = useState(false); // Remove artificial loading
  const [timeRange, setTimeRange] = useState("daily"); // daily, weekly, monthly

  // Remove artificial loading to prevent Fast Refresh issues

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 500); // Reduced timeout
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-blue-700 dark:text-blue-300 flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-blue-500" />
            Panel de Estadísticas
          </h1>
          <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
            Monitoreo de recursos y análisis de uso de la aplicación
          </p>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex items-center bg-white dark:bg-black/50 border border-gray-200 dark:border-blue-900/30 rounded-md overflow-hidden">
            <Button
              variant="ghost"
              size="sm"
              className={`px-3 py-1 rounded-none ${
                timeRange === "daily"
                  ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
                  : "text-gray-600 dark:text-gray-400"
              }`}
              onClick={() => setTimeRange("daily")}
            >
              Diario
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className={`px-3 py-1 rounded-none ${
                timeRange === "weekly"
                  ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
                  : "text-gray-600 dark:text-gray-400"
              }`}
              onClick={() => setTimeRange("weekly")}
            >
              Semanal
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className={`px-3 py-1 rounded-none ${
                timeRange === "monthly"
                  ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
                  : "text-gray-600 dark:text-gray-400"
              }`}
              onClick={() => setTimeRange("monthly")}
            >
              Mensual
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center gap-1"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs
        defaultValue="system-resources"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-3 mb-6 w-full">
          <TabsTrigger
            value="system-resources"
            className="flex items-center gap-2"
          >
            <Cpu className="h-4 w-4" />
            <span className="hidden sm:inline">Recursos del Sistema</span>
            <span className="sm:hidden">Sistema</span>
          </TabsTrigger>
          <TabsTrigger
            value="application-usage"
            className="flex items-center gap-2"
          >
            <Activity className="h-4 w-4" />
            <span className="hidden sm:inline">Uso de la Aplicación</span>
            <span className="sm:hidden">Uso</span>
          </TabsTrigger>
          <TabsTrigger
            value="user-behavior"
            className="flex items-center gap-2"
          >
            <LayoutDashboard className="h-4 w-4" />
            <span className="hidden sm:inline">Comportamiento del Usuario</span>
            <span className="sm:hidden">Usuario</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="system-resources" className="space-y-6">
          <SystemResourcesPanel timeRange={timeRange} loading={loading} />
        </TabsContent>

        <TabsContent value="application-usage" className="space-y-6">
          <ApplicationUsagePanel timeRange={timeRange} loading={loading} />
        </TabsContent>

        <TabsContent value="user-behavior" className="space-y-6">
          <UserBehaviorPanel timeRange={timeRange} loading={loading} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
