/** @type {import('next').NextConfig} */

// Desktop-only Tauri application configuration
const isProd = process.env.NODE_ENV === 'production';

const nextConfig = {
  // Essential Tauri desktop configuration
  output: isProd ? 'export' : undefined, // Only export for production builds
  distDir: isProd ? 'out' : '.next', // Use standard .next for development
  trailingSlash: isProd, // Only use trailing slash for production builds

  // Image optimization must be disabled for static export
  images: {
    unoptimized: true,
  },

  // Asset prefix configuration for development
  assetPrefix: isProd ? undefined : undefined, // Remove asset prefix for development

  // Disable x-powered-by header for security
  poweredByHeader: false,

  // Desktop-only environment variables
  env: {
    TAURI_PLATFORM: 'desktop',
    IS_TAURI: 'true',
  },

  // Compiler options
  compiler: {
    // Remove console.log in production
    removeConsole: isProd ? {
      exclude: ['error']
    } : false,
  },

  // Development optimizations
  typescript: {
    // Don't fail build on TS errors during development
    ignoreBuildErrors: !isProd,
  },
  eslint: {
    // Don't fail build on ESLint errors during development
    ignoreDuringBuilds: !isProd,
  },
  // Experimental features for better performance
  experimental: {
    // Enable modern build output
    esmExternals: true,
    // Optimize package imports
    optimizePackageImports: [
      '@radix-ui/react-icons',
      '@heroicons/react',
      'lucide-react',
      'framer-motion',
    ],
    // Optimize Fast Refresh to prevent full reloads
    optimizeServerReact: true,
    // Improve development performance
    serverComponentsExternalPackages: ['@tauri-apps/api'],
  },

  // Webpack configuration for Tauri compatibility
  webpack: (config, { dev, isServer }) => {
    // SVG handling
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // Client-side specific configurations
    if (!isServer) {
      // Node.js polyfills for browser
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        buffer: false,
        util: false,
        url: false,
        querystring: false,
      };      // Handle Tauri API as external dependency
      config.externals = config.externals || [];
      config.externals.push(function({ request }, callback) {
        // Tauri API modules - treat as external to prevent webpack bundling
        if (/^@tauri-apps\/api($|\/.*)/.test(request)) {
          return callback(null, 'commonjs ' + request);
        }

        // Tauri plugins
        if (/^@tauri-apps\/plugin-.*/.test(request)) {
          return callback(null, 'commonjs ' + request);
        }

        callback();
      });

      // Optimize chunks for better performance
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              enforce: true,
            },
          },
        },
      };
    }

    // Production optimizations
    if (isProd && !isServer) {
      // Add bundle analyzer if needed
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
          })
        );
      }

      // Optimize for size
      config.optimization = {
        ...config.optimization,
        usedExports: true,
        sideEffects: false,
        providedExports: true,
      };
    }

    // Development optimizations
    if (dev) {
      // Faster rebuilds and prevent unnecessary reloads
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: [__filename],
        },
      };

      // Optimize Fast Refresh to prevent full reloads
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }

    return config;
  },
  // Temporarily disable React strict mode to prevent Fast Refresh issues
  reactStrictMode: false,

  // SWC minify for better performance
  swcMinify: true,
};

module.exports = nextConfig;
