{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 518316156175557943, "deps": [[3060637413840920116, "proc_macro2", false, 8912137790616353475], [7341521034400937459, "tauri_codegen", false, 11684803795440256164], [11050281405049894993, "tauri_utils", false, 15945766851994530379], [13077543566650298139, "heck", false, 6236491847068976086], [17990358020177143287, "quote", false, 10839489564739371623], [18149961000318489080, "syn", false, 4542244710357721814]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-9b950e11002ddaa8\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}