{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 1792614923475313633, "deps": [[1462335029370885857, "quick_xml", false, 5942791145987146994], [3334271191048661305, "windows_version", false, 12216679760242905195], [10806645703491011684, "thiserror", false, 15557766392623119489], [13116089016666501665, "windows", false, 13473427962125974306]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-5b622199f64c7a85\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}