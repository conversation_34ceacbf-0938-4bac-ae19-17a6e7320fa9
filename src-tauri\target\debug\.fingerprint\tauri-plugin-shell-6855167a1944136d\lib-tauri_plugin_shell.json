{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 14946440181512331407, "deps": [[500211409582349667, "shared_child", false, 14010360189599177544], [1582828171158827377, "build_script_build", false, 3289333479496358068], [5986029879202738730, "log", false, 5130091804064732190], [7303982924001358866, "tokio", false, 1342577411973725596], [9451456094439810778, "regex", false, 10414513821653749488], [9689903380558560274, "serde", false, 9029599480544516048], [10755362358622467486, "tauri", false, 11449650272081862227], [10806645703491011684, "thiserror", false, 15557766392623119489], [10858870125947963128, "os_pipe", false, 17094772785254387262], [14564311161534545801, "encoding_rs", false, 14150080095936735141], [15367738274754116744, "serde_json", false, 1424998972824055668], [16192041687293812804, "open", false, 6940435854561286154]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-6855167a1944136d\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}