import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Only log redirects to reduce console noise
  const shouldLog = process.env.NODE_ENV === 'development';

  // Handle trailing slash issues
  if (pathname !== '/' && pathname.endsWith('/')) {
    const newUrl = new URL(request.url);
    newUrl.pathname = pathname.slice(0, -1);
    if (shouldLog) console.log(`[Middleware] Redirecting ${pathname} to ${newUrl.pathname}`);
    return NextResponse.redirect(newUrl, 301); // Permanent redirect
  }

  // Handle specific route redirects
  if (pathname === '/visualizador') {
    const newUrl = new URL('/tools/canal-parametrico', request.url);
    if (shouldLog) console.log(`[Middleware] Redirecting ${pathname} to ${newUrl.pathname}`);
    return NextResponse.redirect(newUrl, 301); // Permanent redirect
  }

  if (pathname === '/caudales') {
    const newUrl = new URL('/tools/canal-parametrico', request.url);
    if (shouldLog) console.log(`[Middleware] Redirecting ${pathname} to ${newUrl.pathname}`);
    return NextResponse.redirect(newUrl, 301); // Permanent redirect
  }

  // Allow the request to continue
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
