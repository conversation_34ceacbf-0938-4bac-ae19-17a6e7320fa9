{"rustc": 10895048813736897673, "features": "[\"devtools\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 17459901917850216397, "deps": [[442785307232013896, "build_script_build", false, 12822898391901419538], [3150220818285335163, "url", false, 10302015290578283650], [4143744114649553716, "raw_window_handle", false, 6880312038169398310], [7606335748176206944, "dpi", false, 10462780868951651767], [9010263965687315507, "http", false, 11912463509730526472], [9689903380558560274, "serde", false, 9029599480544516048], [10806645703491011684, "thiserror", false, 15557766392623119489], [11050281405049894993, "tauri_utils", false, 15791428986117739117], [13116089016666501665, "windows", false, 13473427962125974306], [15367738274754116744, "serde_json", false, 1424998972824055668], [16727543399706004146, "cookie", false, 10065597922815552321]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-6fa1cf3ec807e20b\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}