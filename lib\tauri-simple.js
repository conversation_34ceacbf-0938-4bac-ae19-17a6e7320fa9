// tauri-simple.js
// Helper module for Tauri v2 imports with proper error handling

let tauriCore = null;
let tauriEvent = null;
let tauriWindow = null;
let tauriWebview = null;

// Initialize Tauri modules
async function initTauri() {
  if (!window.__TAURI__) {
    throw new Error("Tauri API not available");
  }

  try {
    // For Tauri v2, the APIs are available directly on window.__TAURI__
    tauriCore = window.__TAURI__.core;
    tauriEvent = window.__TAURI__.event;
    tauriWindow = window.__TAURI__.window;
    tauriWebview = window.__TAURI__.webview;
    
    console.log("Tauri modules initialized successfully");
    return true;
  } catch (error) {
    console.error("Failed to initialize Tauri modules:", error);
    throw error;
  }
}

// Safe invoke wrapper
export async function invoke(cmd, args) {
  try {
    if (!tauriCore) {
      await initTauri();
    }
    console.log(`Invoking command: ${cmd}`, args);
    const result = await tauriCore.invoke(cmd, args);
    console.log(`Command ${cmd} completed successfully`);
    return result;
  } catch (error) {
    console.error(`Failed to invoke ${cmd}:`, error);
    throw error;
  }
}

// Safe listen wrapper
export async function listen(event, handler) {
  try {
    if (!tauriEvent) {
      await initTauri();
    }
    console.log(`Setting up listener for event: ${event}`);
    return await tauriEvent.listen(event, handler);
  } catch (error) {
    console.error(`Failed to listen to ${event}:`, error);
    throw error;
  }
}

// Get current window
export async function getCurrentWindow() {
  try {
    if (!tauriWindow) {
      await initTauri();
    }
    return tauriWindow.getCurrentWindow();
  } catch (error) {
    console.error("Failed to get current window:", error);
    throw error;
  }
}

// Get current webview window
export async function getCurrentWebviewWindow() {
  try {
    if (!tauriWebview) {
      await initTauri();
    }
    return tauriWebview.getCurrentWebviewWindow();
  } catch (error) {
    console.error("Failed to get current webview window:", error);
    throw error;
  }
}

// Check app status
export async function checkAppStatus() {
  return invoke("check_app_status");
}

// Get system info
export async function getSystemInfo() {
  return invoke("get_system_info");
}

// Initialize on load
if (typeof window !== 'undefined' && window.__TAURI__) {
  initTauri().catch(console.error);
}

// Export for compatibility
export default { 
  invoke, 
  listen, 
  getCurrentWindow, 
  getCurrentWebviewWindow,
  checkAppStatus, 
  getSystemInfo 
};