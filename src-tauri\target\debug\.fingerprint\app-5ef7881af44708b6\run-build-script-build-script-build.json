{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 16743376101199987636], [10755362358622467486, "build_script_build", false, 933888942641122212], [3834743577069889284, "build_script_build", false, 7555153866226533128], [7236291379133587555, "build_script_build", false, 11181708290338227979], [12783828711503588811, "build_script_build", false, 16310155232584728214], [1582828171158827377, "build_script_build", false, 3289333479496358068], [5943080732378436368, "build_script_build", false, 5222206441672303744]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-5ef7881af44708b6\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}