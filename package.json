{"name": "hydra21", "version": "0.1.0", "private": true, "scripts": {"dev": "tauri dev", "build": "tauri build", "start": "tauri dev", "lint": "next lint", "clean": "rim<PERSON>f .next out target", "type-check": "tsc --noEmit", "tauri": "tauri", "desktop:dev": "tauri dev", "desktop:build": "tauri build", "desktop:bundle": "tauri build --bundles all"}, "dependencies": {"@heroicons/react": "^2.1.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.4", "@react-three/drei": "^9.99.0", "@react-three/fiber": "^8.15.16", "@tauri-apps/api": "^2.1.0", "@types/d3": "^7.4.3", "chart.js": "^4.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "katex": "^0.16.22", "lucide-react": "^0.507.0", "motion": "^12.10.0", "next": "^14.2.28", "next-themes": "^0.2.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-rnd": "^10.5.2", "react-use-measure": "^2.1.7", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "rimraf": "^6.0.1", "shiki": "^3.4.0", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@tauri-apps/cli": "^2.1.0", "@types/node": "^20", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}