{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 10458293153595023085, "deps": [[3060637413840920116, "proc_macro2", false, 8912137790616353475], [3150220818285335163, "url", false, 8654848911607671590], [4899080583175475170, "semver", false, 8460054862119792566], [7170110829644101142, "json_patch", false, 10006666569786601531], [7392050791754369441, "ico", false, 4805278059224830147], [8269115081296425610, "uuid", false, 15191830765756099840], [9689903380558560274, "serde", false, 13637551763077354247], [9857275760291862238, "sha2", false, 14872329733655386886], [10806645703491011684, "thiserror", false, 15557766392623119489], [11050281405049894993, "tauri_utils", false, 15945766851994530379], [12687914511023397207, "png", false, 17313643437940603821], [13077212702700853852, "base64", false, 14860245615036220803], [14132538657330703225, "brotli", false, 9832872640995531574], [15367738274754116744, "serde_json", false, 16296884410147550153], [15622660310229662834, "walkdir", false, 5604292734920322603], [17990358020177143287, "quote", false, 10839489564739371623], [18149961000318489080, "syn", false, 4542244710357721814]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-72f92b49e4066fd7\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}