"use client";

import React, { useState, useEffect } from "react";
import { AnimatePresence } from "framer-motion";
import AnimatedSplashScreen from "./AnimatedSplashScreen";
import OnboardingPage from "./OnboardingPage";
import { storageService } from "@/lib/storage-service";
import { useSystemSettings } from "@/contexts/SystemSettingsContext";
import PerformanceMonitor from "./developer/PerformanceMonitor";
import DevInfo from "./developer/DevInfo";
import TitleBar from "./TitleBarNew";
import { useNativeTheme } from "@/hooks/useNativeTheme";

interface AppWrapperProps {
  children: React.ReactNode;
}

const AppWrapper: React.FC<AppWrapperProps> = ({ children }) => {
  // Initialize state to prevent hydration mismatch - all start as false
  const [showSplash, setShowSplash] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [appReady, setAppReady] = useState(false);
  const { settings } = useSystemSettings();
  const { isMacOS, isWindows, isLinux } = useNativeTheme();

  // Single initialization effect that runs only once
  useEffect(() => {
    let isMounted = true;

    const initializeApp = async () => {
      try {
        console.log("🚀 Starting app initialization...");

        // Apply platform-specific classes
        if (typeof window !== "undefined") {
          if (isMacOS) document.body.classList.add('macos');
          if (isWindows) document.body.classList.add('windows');
          if (isLinux) document.body.classList.add('linux');
        }

        // Check if we're in Tauri environment
        const isTauriEnv = typeof window !== "undefined" &&
                          ((window as any).__TAURI_IPC__ ||
                           document.querySelector('meta[name="tauri-version"]') ||
                           window.location.protocol === 'tauri:');

        // Initialize storage service
        await storageService.initialize();

        // Get current app state
        const isFirstVisit = storageService.isFirstVisit();
        const hasSplashBeenShown = storageService.hasSplashBeenShown();
        const onboardingCompleted = await storageService.isOnboardingCompleted();

        console.log("📊 App State:", {
          isFirstVisit,
          hasSplashBeenShown,
          onboardingCompleted,
          isTauriEnv
        });

        // Only update state if component is still mounted
        if (!isMounted) return;

        // Determine what to show based on state
        if (isTauriEnv) {
          // In Tauri, skip splash and go directly to content or onboarding
          console.log("🖥️ Tauri environment - skipping splash screen");
          setShowSplash(false);
          setShowOnboarding(!onboardingCompleted);
          setAppReady(onboardingCompleted);
        } else if (isFirstVisit && !hasSplashBeenShown) {
          // First visit - show splash screen
          console.log("✨ First visit detected - showing splash screen");
          setShowSplash(true);
          setShowOnboarding(false);
          setAppReady(false);
          // Mark that we've shown the splash
          storageService.markFirstVisitCompleted();
        } else if (!onboardingCompleted) {
          // Returning user who hasn't completed onboarding
          console.log("👤 Returning user - showing onboarding");
          setShowSplash(false);
          setShowOnboarding(true);
          setAppReady(false);
        } else {
          // Returning user with completed setup - show app directly
          console.log("✅ Returning user - showing app directly");
          setShowSplash(false);
          setShowOnboarding(false);
          setAppReady(true);
        }

        setIsInitialized(true);
        console.log("🎉 App initialization completed");

      } catch (error) {
        console.error("❌ Error during app initialization:", error);
        if (isMounted) {
          // On error, show app directly to prevent blank screen
          setShowSplash(false);
          setShowOnboarding(false);
          setAppReady(true);
          setIsInitialized(true);
        }
      }
    };

    // Reduce delay to minimize Fast Refresh issues
    const initTimer = setTimeout(initializeApp, 50);

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(initTimer);
    };
  }, []); // Empty dependency array - run only once

  // Reduced fallback timeout to prevent Fast Refresh issues
  useEffect(() => {
    const fallbackTimer = setTimeout(() => {
      if (!isInitialized) {
        console.warn("⚠️ Initialization timeout - forcing app to show");
        setShowSplash(false);
        setShowOnboarding(false);
        setAppReady(true);
        setIsInitialized(true);
      }
    }, 1500); // Reduced from 3000ms

    return () => clearTimeout(fallbackTimer);
  }, [isInitialized]);

  // Handle splash screen finish
  const handleSplashFinish = async () => {
    try {
      console.log("🎬 Splash screen finished");
      setShowSplash(false);

      // Check if we need to show onboarding
      const onboardingCompleted = await storageService.isOnboardingCompleted();
      if (!onboardingCompleted) {
        console.log("📋 Showing onboarding after splash");
        setShowOnboarding(true);
        setAppReady(false);
      } else {
        console.log("✅ Onboarding already completed - showing app");
        setShowOnboarding(false);
        setAppReady(true);
      }
    } catch (error) {
      console.error("❌ Error handling splash finish:", error);
      // On error, show app directly
      setShowSplash(false);
      setShowOnboarding(false);
      setAppReady(true);
    }
  };

  // Handle onboarding completion
  const handleOnboardingComplete = async () => {
    try {
      console.log("✅ Onboarding completed");
      await storageService.setOnboardingCompleted(true);
      setShowOnboarding(false);
      setAppReady(true);
    } catch (error) {
      console.error("❌ Error completing onboarding:", error);
      // Still proceed to show the app
      setShowOnboarding(false);
      setAppReady(true);
    }
  };

  // Debug mode: URL parameters for testing
  useEffect(() => {
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);

      // Force onboarding for testing
      if (urlParams.has("force_onboarding")) {
        console.log("🔧 Debug: Forcing onboarding");
        setShowSplash(false);
        setShowOnboarding(true);
        setAppReady(false);
      }

      // Reset app state for testing
      if (urlParams.has("reset_app")) {
        console.log("🔧 Debug: Resetting app state");
        storageService.resetAppState().then(() => {
          window.location.reload();
        });
      }

      // Show current state for debugging
      if (urlParams.has("debug_state")) {
        console.log("🔧 Debug: Current app state", storageService.getAppState());
      }
    }
  }, []);



  // Don't render anything until initialized to prevent hydration issues
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-blue-950">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-gray-300 text-sm">Inicializando aplicación...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <AnimatePresence mode="wait">
        {showSplash && (
          <AnimatedSplashScreen
            key="splash"
            onFinish={handleSplashFinish}
            duration={2500} // Reduced duration
          />
        )}

        {!showSplash && showOnboarding && (
          <OnboardingPage
            key="onboarding"
            onComplete={handleOnboardingComplete}
          />
        )}
      </AnimatePresence>

      {/* Main app content - only show when ready */}
      {appReady && (
        <div
          className={`
            ${settings.displayMode === "compact" ? "display-compact" : ""}
            ${settings.displayMode === "comfortable" ? "display-comfortable" : ""}
            ${settings.performanceMode === "performance" ? "performance-high" : ""}
            ${settings.performanceMode === "efficiency" ? "performance-efficiency" : ""}
            ${settings.developerMode ? "developer-mode" : ""}
            window-root
          `}
          style={{ height: "100%" }}
        >
          {/* Custom title bar for desktop Tauri app */}
          {(isWindows || isMacOS || isLinux) && <TitleBar />}

          {/* Developer tools */}
          {settings.developerMode && <PerformanceMonitor />}
          {settings.developerMode && <DevInfo />}

          {/* Main app content */}
          {children}
        </div>
      )}
    </>
  );
};

export default AppWrapper;
