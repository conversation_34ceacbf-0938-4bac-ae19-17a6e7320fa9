{"rustc": 10895048813736897673, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 16305054664657135572, "deps": [[40386456601120721, "percent_encoding", false, 16923689341187554613], [95042085696191081, "ipnet", false, 12219153719656379687], [264090853244900308, "sync_wrapper", false, 831687397342849363], [784494742817713399, "tower_service", false, 13446837542646308114], [1906322745568073236, "pin_project_lite", false, 3062512441202075933], [3150220818285335163, "url", false, 10302015290578283650], [3722963349756955755, "once_cell", false, 17797913802642721045], [4405182208873388884, "http", false, 16572521042505323057], [5986029879202738730, "log", false, 5130091804064732190], [7303982924001358866, "tokio", false, 1342577411973725596], [7414427314941361239, "hyper", false, 18335546487838486540], [7620660491849607393, "futures_core", false, 9626795764498236188], [8405603588346937335, "winreg", false, 3378730781935311637], [8915503303801890683, "http_body", false, 5511496457226507628], [9689903380558560274, "serde", false, 9029599480544516048], [10229185211513642314, "mime", false, 3243225773130811609], [10629569228670356391, "futures_util", false, 8791724364475240372], [12186126227181294540, "tokio_native_tls", false, 6334084830227294517], [12367227501898450486, "hyper_tls", false, 4269277738362947818], [13809605890706463735, "h2", false, 11299249929074254343], [14564311161534545801, "encoding_rs", false, 14150080095936735141], [15367738274754116744, "serde_json", false, 1424998972824055668], [16066129441945555748, "bytes", false, 2743515925362514019], [16311359161338405624, "rustls_pemfile", false, 13318256937905746467], [16542808166767769916, "serde_urlencoded", false, 17659138722405199539], [16785601910559813697, "native_tls_crate", false, 11229055814804384756], [18066890886671768183, "base64", false, 8247856593956362165]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-a1849bba3cd25b13\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}