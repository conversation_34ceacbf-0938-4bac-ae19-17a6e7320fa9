'use client';

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';

export default function TitleBar() {
  const [isMaximized, setIsMaximized] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [windowTitle, setWindowTitle] = useState('HYDRA²¹ - Desktop Application');
  const [mounted, setMounted] = useState(false);
  const [appWindow, setAppWindow] = useState<any>(null);
  const { theme } = useTheme();

  useEffect(() => {
    setMounted(true);

    if (typeof window === 'undefined' || !window.__TAURI__) {
      console.log("Not in Tauri environment - running in browser mode");
      return;
    }

    console.log("Tauri environment detected, initializing window controls...");

    // Initialize window state
    const initializeWindow = async () => {
      try {
        console.log("Initializing window...");

        // Try multiple import approaches for compatibility
        let currentWindow;
        try {
          // First try the newer webviewWindow API with default import
          const webviewWindowModule = await import('@tauri-apps/api/webviewWindow');
          console.log("Successfully imported webviewWindow module");

          // Try both default export and named export patterns
          const getCurrentWebviewWindow = webviewWindowModule.getCurrentWebviewWindow || webviewWindowModule.default?.getCurrentWebviewWindow;
          if (getCurrentWebviewWindow) {
            currentWindow = getCurrentWebviewWindow();
            console.log("getCurrentWebviewWindow() called successfully");
          } else {
            throw new Error("getCurrentWebviewWindow not found in webviewWindow module");
          }
        } catch (error) {
          console.log("webviewWindow import failed, trying window API:", error);
          // Fallback to window API for older versions
          const windowModule = await import('@tauri-apps/api/window');
          console.log("Successfully imported window module");

          // Try both default export and named export patterns
          const getCurrentWindow = windowModule.getCurrentWindow || windowModule.default?.getCurrentWindow;
          if (getCurrentWindow) {
            currentWindow = getCurrentWindow();
            console.log("getCurrentWindow() called successfully");
          } else {
            throw new Error("getCurrentWindow not found in window module");
          }
        }

        setAppWindow(currentWindow);
        console.log("Window instance:", currentWindow, "Type:", typeof currentWindow);

        // Get initial states
        const [maximized, fullscreen, title] = await Promise.all([
          currentWindow.isMaximized(),
          currentWindow.isFullscreen(),
          currentWindow.title()
        ]);

        setIsMaximized(maximized);
        setIsFullscreen(fullscreen);
        setWindowTitle(title);

        console.log("Window initialized - Maximized:", maximized, "Title:", title);

        // Set up event listeners
        const unlistenResize = await currentWindow.onResized(() => {
          currentWindow.isMaximized().then(setIsMaximized);
        });

        const unlistenFocus = await currentWindow.onFocusChanged(() => {
          currentWindow.isMaximized().then(setIsMaximized);
        });

        // Cleanup function
        return () => {
          unlistenResize();
          unlistenFocus();
        };

      } catch (error) {
        console.error("Error initializing window:", error);
      }
    };

    initializeWindow();
  }, []);

  // Window control handlers
  const handleMinimize = async () => {
    if (typeof window === 'undefined' || !window.__TAURI__) return;

    try {
      console.log("Attempting to minimize...");
      if (appWindow) {
        await appWindow.minimize();
        console.log("Window minimized successfully");
      } else {
        // Fallback: import on demand with compatibility check
        let currentWindow;
        try {
          const webviewWindowModule = await import('@tauri-apps/api/webviewWindow');
          const getCurrentWebviewWindow = webviewWindowModule.getCurrentWebviewWindow || webviewWindowModule.default?.getCurrentWebviewWindow;
          if (getCurrentWebviewWindow) {
            currentWindow = getCurrentWebviewWindow();
          } else {
            throw new Error("getCurrentWebviewWindow not found");
          }
        } catch (error) {
          const windowModule = await import('@tauri-apps/api/window');
          const getCurrentWindow = windowModule.getCurrentWindow || windowModule.default?.getCurrentWindow;
          if (getCurrentWindow) {
            currentWindow = getCurrentWindow();
          } else {
            throw new Error("getCurrentWindow not found");
          }
        }
        await currentWindow.minimize();
        console.log("Window minimized successfully (fallback)");
      }
    } catch (error) {
      console.error("Failed to minimize:", error);
    }
  };

  const handleToggleMaximize = async () => {
    if (typeof window === 'undefined' || !window.__TAURI__) return;

    try {
      console.log("Attempting to toggle maximize...");
      if (appWindow) {
        await appWindow.toggleMaximize();
        const maximized = await appWindow.isMaximized();
        setIsMaximized(maximized);
        console.log("Window toggled successfully - New state:", maximized);
      } else {
        // Fallback: import on demand with compatibility check
        let currentWindow;
        try {
          const webviewWindowModule = await import('@tauri-apps/api/webviewWindow');
          const getCurrentWebviewWindow = webviewWindowModule.getCurrentWebviewWindow || webviewWindowModule.default?.getCurrentWebviewWindow;
          if (getCurrentWebviewWindow) {
            currentWindow = getCurrentWebviewWindow();
          } else {
            throw new Error("getCurrentWebviewWindow not found");
          }
        } catch (error) {
          const windowModule = await import('@tauri-apps/api/window');
          const getCurrentWindow = windowModule.getCurrentWindow || windowModule.default?.getCurrentWindow;
          if (getCurrentWindow) {
            currentWindow = getCurrentWindow();
          } else {
            throw new Error("getCurrentWindow not found");
          }
        }
        await currentWindow.toggleMaximize();
        const maximized = await currentWindow.isMaximized();
        setIsMaximized(maximized);
        console.log("Window toggled successfully (fallback) - New state:", maximized);
      }
    } catch (error) {
      console.error("Failed to toggle maximize:", error);
    }
  };

  const handleClose = async () => {
    if (typeof window === 'undefined' || !window.__TAURI__) return;

    try {
      console.log("Attempting to close...");
      if (appWindow) {
        await appWindow.close();
        console.log("Window closing successfully");
      } else {
        // Fallback: import on demand with compatibility check
        let currentWindow;
        try {
          const webviewWindowModule = await import('@tauri-apps/api/webviewWindow');
          const getCurrentWebviewWindow = webviewWindowModule.getCurrentWebviewWindow || webviewWindowModule.default?.getCurrentWebviewWindow;
          if (getCurrentWebviewWindow) {
            currentWindow = getCurrentWebviewWindow();
          } else {
            throw new Error("getCurrentWebviewWindow not found");
          }
        } catch (error) {
          const windowModule = await import('@tauri-apps/api/window');
          const getCurrentWindow = windowModule.getCurrentWindow || windowModule.default?.getCurrentWindow;
          if (getCurrentWindow) {
            currentWindow = getCurrentWindow();
          } else {
            throw new Error("getCurrentWindow not found");
          }
        }
        await currentWindow.close();
        console.log("Window closing successfully (fallback)");
      }
    } catch (error) {
      console.error("Failed to close:", error);
    }
  };

  // Don't render until mounted
  if (!mounted) {
    return null;
  }

  // Don't render if fullscreen
  if (isFullscreen) {
    return null;
  }

  const isDark = theme === 'dark';

  return (
    <>
      <div
        data-tauri-drag-region
        style={{
          height: '32px',
          backgroundColor: isDark ? '#0c1222' : '#ffffff',
          borderBottom: `1px solid ${isDark ? '#1e293b' : '#e2e8f0'}`,
          color: isDark ? '#f1f5f9' : '#1e293b',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          userSelect: 'none',
          WebkitUserSelect: 'none',
          padding: '0 16px',
          fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, sans-serif',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)'
        }}
        onDoubleClick={handleToggleMaximize}
      >
        {/* Left side - App title */}
        <div
          data-tauri-drag-region
          style={{
            display: 'flex',
            alignItems: 'center',
            flex: 1,
            fontSize: '13px',
            fontWeight: 600,
            letterSpacing: '0.3px'
          }}
        >
          {windowTitle}
        </div>

        {/* Right side - Window controls */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1px'
          }}
        >
          {/* Minimize button */}
          <button
            onClick={handleMinimize}
            title="Minimize"
            style={{
              width: '46px',
              height: '30px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: 'none',
              background: 'transparent',
              color: 'inherit',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'all 0.15s ease',
              WebkitAppRegion: 'no-drag'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
              <path d="M2 5h6" stroke="currentColor" strokeWidth="1" strokeLinecap="round"/>
            </svg>
          </button>

          {/* Maximize/Restore button */}
          <button
            onClick={handleToggleMaximize}
            title={isMaximized ? "Restore" : "Maximize"}
            style={{
              width: '46px',
              height: '30px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: 'none',
              background: 'transparent',
              color: 'inherit',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'all 0.15s ease',
              WebkitAppRegion: 'no-drag'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            {isMaximized ? (
              <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                <path d="M2.5 2.5h5v5h-5v-5z" stroke="currentColor" strokeWidth="1" fill="none"/>
                <path d="M4 4V1.5h5V6.5h-1.5" stroke="currentColor" strokeWidth="1" fill="none"/>
              </svg>
            ) : (
              <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                <rect x="2" y="2" width="6" height="6" stroke="currentColor" strokeWidth="1" fill="none"/>
              </svg>
            )}
          </button>

          {/* Close button */}
          <button
            onClick={handleClose}
            title="Close"
            style={{
              width: '46px',
              height: '30px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: 'none',
              background: 'transparent',
              color: 'inherit',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'all 0.15s ease',
              WebkitAppRegion: 'no-drag'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#ff5f57';
              e.currentTarget.style.color = '#ffffff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = 'inherit';
            }}
          >
            <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
              <path d="M2 2l6 6M8 2l-6 6" stroke="currentColor" strokeWidth="1" strokeLinecap="round"/>
            </svg>
          </button>
        </div>
      </div>

      {/* Global styles */}
      <style jsx global>{`
        /* Ensure buttons don't interfere with dragging */
        [data-tauri-drag-region] button {
          -webkit-app-region: no-drag !important;
        }
      `}</style>
    </>
  );
}