{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 16236511956634820608, "deps": [[4899080583175475170, "semver", false, 8460054862119792566], [6913375703034175521, "schemars", false, 5066447304317806457], [7170110829644101142, "json_patch", false, 10006666569786601531], [8786711029710048183, "toml", false, 16966375114130756422], [9689903380558560274, "serde", false, 13637551763077354247], [11050281405049894993, "tauri_utils", false, 15945766851994530379], [12714016054753183456, "tauri_winres", false, 12599574447935333488], [13077543566650298139, "heck", false, 6236491847068976086], [13475171727366188400, "cargo_toml", false, 14518134395730026219], [13625485746686963219, "anyhow", false, 11700001983982356752], [15367738274754116744, "serde_json", false, 16296884410147550153], [15622660310229662834, "walkdir", false, 5604292734920322603], [16928111194414003569, "dirs", false, 14986364135949624918], [17155886227862585100, "glob", false, 15406231137402787690]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-48d800119fce00a1\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}