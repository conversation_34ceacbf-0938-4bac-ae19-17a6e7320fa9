"use client";

import { useEffect, useState } from "react";
import { SunIcon, MoonIcon } from "lucide-react";
import { useTheme } from "next-themes";

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const isDarkMode = theme === "dark";

  // Necesario para evitar errores de hidratación
  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    const newTheme = isDarkMode ? "light" : "dark";
    setTheme(newTheme);
  };

  // Return a placeholder with same dimensions to prevent layout shift
  if (!mounted) {
    return (
      <div className="inline-flex h-10 w-10 items-center justify-center rounded-md opacity-0">
        <SunIcon className="h-5 w-5" />
      </div>
    );
  }

  return (
    <button
      onClick={toggleTheme}
      className="rounded-lg p-2 bg-blue-600/20 hover:bg-blue-600/30 transition-colors border border-blue-500/30 shadow-lg shadow-blue-500/10"
      aria-label="Cambiar tema"
      title={isDarkMode ? "Cambiar a tema claro" : "Cambiar a tema oscuro"}
    >
      {isDarkMode ? (
        <SunIcon className="h-5 w-5 text-yellow-400" />
      ) : (
        <MoonIcon className="h-5 w-5 text-blue-400" />
      )}
    </button>
  );
}
