"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { invoke, listen } from "@/lib/tauri-simple";

// Desktop-only notification context - no web fallbacks

// Notification types
export type NotificationType =
  | "info"
  | "warning"
  | "success"
  | "message"
  | "system"
  | "calculation"
  | "export"
  | "update"
  | "feature";

// Notification priority
export type NotificationPriority = "low" | "medium" | "high" | "critical";

// Notification action
export interface NotificationAction {
  id: string;
  label: string;
  actionType: string;
  data?: any;
}

// Notification interface
export interface Notification {
  id: string;
  title: string;
  message: string;
  createdAt: string;
  read: boolean;
  notificationType: NotificationType;
  priority: NotificationPriority;
  link?: string;
  actions?: NotificationAction[];
  metadata?: any;
}

// Context interface
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (
    title: string,
    message: string,
    type: NotificationType,
    options?: {
      priority?: NotificationPriority;
      link?: string;
      showSystemNotification?: boolean;
    }
  ) => Promise<string>;
  markAsRead: (id: string) => Promise<boolean>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<boolean>;
  clearNotifications: () => Promise<void>;
  getFilteredNotifications: (filter: string) => Notification[];
}

// Create context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Provider component
export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Initialize notifications
  useEffect(() => {
    const initNotifications = async () => {
      try {
        // Check if we're in Tauri environment
        if (typeof window === 'undefined' || !window.__TAURI__) {
          console.log("Not in Tauri environment, skipping notification initialization");
          setIsInitialized(true);
          return;
        }

        // Get notifications from backend
        try {
          const notificationsData = await invoke<Notification[]>("get_notifications");
          setNotifications(notificationsData || []);
          console.log(`Loaded ${notificationsData?.length || 0} notifications`);
        } catch (error) {
          console.warn("get_notifications command not available:", error);
          setNotifications([]);
        }

        // Get unread count
        try {
          const count = await invoke<number>("get_unread_notification_count");
          setUnreadCount(count || 0);
          console.log(`Unread notification count: ${count || 0}`);
        } catch (error) {
          console.warn("get_unread_notification_count command not available:", error);
          setUnreadCount(0);
        }

        setIsInitialized(true);
        console.log("✅ Notifications initialized successfully");
      } catch (error) {
        console.error("Failed to initialize notifications:", error);
        setNotifications([]);
        setUnreadCount(0);
        setIsInitialized(true);
      }
    };

    initNotifications();
  }, []);

  // Listen for notification events
  useEffect(() => {
    if (!isInitialized || typeof window === 'undefined' || !window.__TAURI__) return;

    let unlistenFns: (() => void)[] = [];

    const setupEventListeners = async () => {
      try {
        // New notification event
        const unlistenNew = await listen<Notification>("notification:new", (event) => {
          console.log("New notification received:", event.payload);
          setNotifications((prev) => [event.payload, ...prev]);
          setUnreadCount((prev) => prev + 1);
        });
        unlistenFns.push(unlistenNew);

        // Updated notification event
        const unlistenUpdated = await listen<string>("notification:updated", (event) => {
          const notificationId = event.payload;
          console.log("Notification updated:", notificationId);
          setNotifications((prev) =>
            prev.map((n) =>
              n.id === notificationId ? { ...n, read: true } : n
            )
          );
          setUnreadCount((prev) => Math.max(0, prev - 1));
        });
        unlistenFns.push(unlistenUpdated);

        // All read event
        const unlistenAllRead = await listen("notification:all_read", () => {
          console.log("All notifications marked as read");
          setNotifications((prev) =>
            prev.map((n) => ({ ...n, read: true }))
          );
          setUnreadCount(0);
        });
        unlistenFns.push(unlistenAllRead);

        // Deleted notification event
        const unlistenDeleted = await listen<string>("notification:deleted", (event) => {
          const notificationId = event.payload;
          console.log("Notification deleted:", notificationId);
          setNotifications((prev) => {
            const notification = prev.find((n) => n.id === notificationId);
            const newNotifications = prev.filter((n) => n.id !== notificationId);

            if (notification && !notification.read) {
              setUnreadCount((prev) => Math.max(0, prev - 1));
            }

            return newNotifications;
          });
        });
        unlistenFns.push(unlistenDeleted);

        // Cleared notifications event
        const unlistenCleared = await listen("notification:cleared", () => {
          console.log("All notifications cleared");
          setNotifications([]);
          setUnreadCount(0);
        });
        unlistenFns.push(unlistenCleared);

        console.log("✅ Notification event listeners setup complete");
      } catch (error) {
        console.error("Error setting up event listeners:", error);
      }
    };

    setupEventListeners();

    // Cleanup
    return () => {
      unlistenFns.forEach(fn => {
        try {
          fn();
        } catch (error) {
          console.error("Error cleaning up listener:", error);
        }
      });
    };
  }, [isInitialized]);

  // Add notification
  const addNotification = async (
    title: string,
    message: string,
    type: NotificationType,
    options?: {
      priority?: NotificationPriority;
      link?: string;
      showSystemNotification?: boolean;
    }
  ): Promise<string> => {
    try {
      const id = await invoke<string>("add_notification", {
        title,
        message,
        notificationType: type,
        priority: options?.priority || "medium",
        link: options?.link,
        showSystemNotification: options?.showSystemNotification || false,
      });
      console.log(`Notification added with ID: ${id}`);
      return id || "";
    } catch (error) {
      console.error("Failed to add notification:", error);
      throw error;
    }
  };

  // Mark notification as read
  const markAsRead = async (id: string): Promise<boolean> => {
    try {
      const result = await invoke<boolean>("mark_notification_as_read", {
        notificationId: id,
      });
      console.log(`Notification ${id} marked as read: ${result}`);
      return result || false;
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async (): Promise<void> => {
    try {
      await invoke("mark_all_notifications_as_read");
      console.log("All notifications marked as read");
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
    }
  };

  // Delete notification
  const deleteNotification = async (id: string): Promise<boolean> => {
    try {
      const result = await invoke<boolean>("delete_notification", {
        notificationId: id,
      });
      console.log(`Notification ${id} deleted: ${result}`);
      return result || false;
    } catch (error) {
      console.error("Failed to delete notification:", error);
      return false;
    }
  };

  // Clear all notifications
  const clearNotifications = async (): Promise<void> => {
    try {
      await invoke("clear_notifications");
      console.log("All notifications cleared");
    } catch (error) {
      console.error("Failed to clear notifications:", error);
    }
  };

  // Get filtered notifications
  const getFilteredNotifications = (filter: string): Notification[] => {
    if (filter === "all") return notifications;
    if (filter === "unread") return notifications.filter((n) => !n.read);
    return notifications.filter((n) => n.notificationType === filter);
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        clearNotifications,
        getFilteredNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

// Hook to use the notification context
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider");
  }
  return context;
}